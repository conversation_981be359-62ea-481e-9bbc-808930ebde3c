#!/bin/bash

# 本地 Gemini CLI 启动脚本
# 使用修改后的版本，支持 OpenAI、Claude 和 DeepSeek

echo "🚀 启动本地 Gemini CLI（支持多模型）..."
echo ""

# 检查是否有 .env 文件
if [ ! -f ".env" ]; then
    echo "⚠️  未找到 .env 文件，创建示例配置..."
    cat > .env << EOF
# 选择一个或多个 API Key
# DEEPSEEK_API_KEY=sk-your-deepseek-api-key
# OPENAI_API_KEY=sk-your-openai-api-key
# CLAUDE_API_KEY=sk-ant-your-claude-api-key
# GEMINI_API_KEY=your-gemini-api-key

# 可选：指定默认模型
# GEMINI_MODEL=deepseek-chat
EOF
    echo "✅ 已创建 .env 文件，请编辑并添加你的 API Key"
    echo ""
fi

# 显示支持的模型
echo "📋 支持的鉴权方式："
echo "   • Google 个人账户 OAuth"
echo "   • Gemini API Key"
echo "   • OpenAI API Key"
echo "   • Claude API Key"
echo "   • DeepSeek API Key"
echo "   • Vertex AI"
echo ""

# 显示使用示例
echo "💡 使用示例："
echo "   # 交互模式"
echo "   node bundle/gemini.js"
echo ""
echo "   # 直接命令"
echo "   node bundle/gemini.js \"写一个快速排序算法\""
echo "   node bundle/gemini.js -p \"解释这段代码\""
echo ""
echo "   # 指定模型"
echo "   node bundle/gemini.js -m deepseek-coder \"优化这段代码\""
echo "   node bundle/gemini.js -m gpt-4o \"解释机器学习\""
echo "   node bundle/gemini.js -m claude-3-5-sonnet-20241022 \"分析这个项目\""
echo ""
echo "   # 管道输入"
echo "   echo \"分析这段代码\" | node bundle/gemini.js"
echo "   cat file.js | node bundle/gemini.js \"解释代码\""
echo "   git diff | node bundle/gemini.js \"总结变更\""
echo ""
echo "   # 调试模式"
echo "   node bundle/gemini.js -d \"测试消息\""
echo ""
echo "   # 沙盒模式"
echo "   node bundle/gemini.js -s \"安全执行脚本\""
echo ""

# 启动应用
if [ $# -eq 0 ]; then
    # 无参数，启动交互模式
    echo "🎯 启动交互模式..."
    node bundle/gemini.js
else
    # 有参数，直接执行命令
    echo "🎯 执行命令: $*"
    node bundle/gemini.js "$@"
fi
