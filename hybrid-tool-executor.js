#!/usr/bin/env node

/**
 * 混合工具执行器
 * 1. 先调用 Gemini CLI 获取 AI 分析和建议
 * 2. 解析 AI 响应中的工具调用建议
 * 3. 自动执行相应的工具操作
 */

import { spawn, execSync } from 'child_process';
import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { dirname, resolve } from 'path';

/**
 * AI 响应解析器 - 从 AI 响应中提取工具调用信息
 */
class AIResponseParser {
  parseToolCalls(aiResponse) {
    console.log('🔍 解析 AI 响应中的工具调用...');
    
    const toolCalls = [];
    
    // 解析文件创建建议
    const createFileMatches = aiResponse.match(/(?:创建|写入|生成).*?文件.*?`([^`]+)`.*?内容.*?```(\w+)?\n([\s\S]*?)```/gi);
    if (createFileMatches) {
      for (const match of createFileMatches) {
        const filePathMatch = match.match(/`([^`]+)`/);
        const codeMatch = match.match(/```(?:\w+)?\n([\s\S]*?)```/);
        
        if (filePathMatch && codeMatch) {
          toolCalls.push({
            tool: 'write_file',
            params: {
              file_path: filePathMatch[1],
              content: codeMatch[1].trim()
            }
          });
        }
      }
    }
    
    // 解析文件读取建议
    const readFileMatches = aiResponse.match(/(?:读取|查看|分析).*?文件.*?`([^`]+)`/gi);
    if (readFileMatches) {
      for (const match of readFileMatches) {
        const filePathMatch = match.match(/`([^`]+)`/);
        if (filePathMatch) {
          toolCalls.push({
            tool: 'read_file',
            params: {
              file_path: filePathMatch[1]
            }
          });
        }
      }
    }
    
    // 解析命令执行建议
    const commandMatches = aiResponse.match(/(?:执行|运行).*?命令.*?`([^`]+)`/gi);
    if (commandMatches) {
      for (const match of commandMatches) {
        const commandMatch = match.match(/`([^`]+)`/);
        if (commandMatch) {
          toolCalls.push({
            tool: 'shell',
            params: {
              command: commandMatch[1]
            }
          });
        }
      }
    }
    
    // 智能推断：如果 AI 提到了具体的代码实现
    if (aiResponse.includes('C++') && aiResponse.includes('冒泡排序')) {
      const cppCodeMatch = aiResponse.match(/```(?:cpp|c\+\+)?\n([\s\S]*?)```/i);
      if (cppCodeMatch) {
        toolCalls.push({
          tool: 'write_file',
          params: {
            file_path: 'bubble_sort.cpp',
            content: cppCodeMatch[1].trim()
          }
        });
      }
    }
    
    console.log(`✅ 解析到 ${toolCalls.length} 个工具调用`);
    return toolCalls;
  }
}

/**
 * 工具执行器
 */
class ToolExecutor {
  async executeToolCall(toolCall) {
    const { tool, params } = toolCall;
    
    console.log(`🔧 执行工具: ${tool}`);
    console.log(`📋 参数:`, JSON.stringify(params, null, 2));
    
    try {
      switch (tool) {
        case 'write_file':
          return await this.writeFile(params.file_path, params.content);
        case 'read_file':
          return await this.readFile(params.file_path);
        case 'shell':
          return await this.executeCommand(params.command);
        default:
          console.log(`❌ 未知工具: ${tool}`);
          return null;
      }
    } catch (error) {
      console.error(`❌ 工具执行失败: ${error.message}`);
      return null;
    }
  }
  
  async writeFile(filePath, content) {
    console.log(`📝 创建文件: ${filePath}`);
    
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
    
    writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 文件创建成功: ${resolve(filePath)}`);
    console.log(`📊 文件大小: ${content.length} 字符`);
    
    return { success: true, filePath: resolve(filePath) };
  }
  
  async readFile(filePath) {
    console.log(`📖 读取文件: ${filePath}`);
    
    if (!existsSync(filePath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      return { success: false, error: 'File not found' };
    }
    
    const content = readFileSync(filePath, 'utf8');
    console.log(`✅ 文件读取成功`);
    console.log(`📊 文件大小: ${content.length} 字符`);
    
    return { success: true, content, filePath };
  }
  
  async executeCommand(command) {
    console.log(`💻 执行命令: ${command}`);
    
    try {
      const output = execSync(command, { 
        encoding: 'utf8',
        cwd: process.cwd(),
        maxBuffer: 1024 * 1024
      });
      console.log(`✅ 命令执行成功`);
      console.log(`📋 输出:`);
      console.log(output);
      return { success: true, output };
    } catch (error) {
      console.error(`❌ 命令执行失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 混合执行器主类
 */
class HybridExecutor {
  constructor() {
    this.parser = new AIResponseParser();
    this.toolExecutor = new ToolExecutor();
  }
  
  async execute(request) {
    console.log('🚀 启动混合执行器...');
    console.log(`📝 用户请求: "${request}"`);

    // 预处理：检查是否需要先读取文件
    const needsFileRead = this.checkIfNeedsFileRead(request);
    let fileContent = '';

    if (needsFileRead) {
      console.log('\n📖 预处理: 读取相关文件...');
      fileContent = await this.preReadFiles(request);
    }

    // 第一步：调用 Gemini CLI 获取 AI 分析
    console.log('\n🤖 第一步: 获取 AI 分析...');
    const enhancedRequest = fileContent ? `${request}\n\n文件内容:\n${fileContent}` : request;
    const aiResponse = await this.callGeminiCLI(enhancedRequest);

    if (!aiResponse) {
      console.log('❌ 无法获取 AI 响应，尝试基于请求直接执行...');
      return await this.fallbackExecution(request, fileContent);
    }

    console.log('\n📋 AI 响应:');
    console.log('─'.repeat(50));
    console.log(aiResponse);
    console.log('─'.repeat(50));

    // 第二步：解析 AI 响应中的工具调用
    console.log('\n🔍 第二步: 解析工具调用...');
    const toolCalls = this.parser.parseToolCalls(aiResponse);

    if (toolCalls.length === 0) {
      console.log('ℹ️  AI 响应中未发现可执行的工具调用');
      console.log('💡 尝试智能推断执行...');
      return await this.intelligentInference(request, aiResponse, fileContent);
    }

    // 第三步：执行工具调用
    console.log('\n⚡ 第三步: 执行工具调用...');
    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      console.log(`\n[${i + 1}/${toolCalls.length}] 执行工具调用:`);
      await this.toolExecutor.executeToolCall(toolCall);
    }

    console.log('\n🎉 混合执行完成!');
  }
  
  // 检查是否需要读取文件
  checkIfNeedsFileRead(request) {
    return request.includes('读取') && (request.includes('.py') || request.includes('.js') || request.includes('.cpp'));
  }

  // 预读取文件
  async preReadFiles(request) {
    const fileMatches = request.match(/([^\s]+\.(?:py|js|cpp|ts|txt|md))/g);
    if (!fileMatches) return '';

    let content = '';
    for (const filePath of fileMatches) {
      if (existsSync(filePath)) {
        console.log(`📖 读取文件: ${filePath}`);
        const fileContent = readFileSync(filePath, 'utf8');
        content += `\n=== ${filePath} ===\n${fileContent}\n`;
      }
    }
    return content;
  }

  // 智能推断执行
  async intelligentInference(request, aiResponse, fileContent) {
    console.log('🧠 智能推断执行...');

    // 如果 AI 响应包含 C++ 代码，自动创建文件
    if (request.includes('C++') && aiResponse.includes('```cpp')) {
      const cppCodeMatch = aiResponse.match(/```cpp\n([\s\S]*?)```/);
      if (cppCodeMatch) {
        console.log('🎯 检测到 C++ 代码，自动创建文件...');
        await this.toolExecutor.executeToolCall({
          tool: 'write_file',
          params: {
            file_path: 'bubble_sort.cpp',
            content: cppCodeMatch[1].trim()
          }
        });
        return;
      }
    }

    // 如果 AI 响应包含其他代码，尝试推断文件名
    const codeMatches = aiResponse.match(/```(\w+)\n([\s\S]*?)```/g);
    if (codeMatches) {
      for (const match of codeMatches) {
        const [, lang, code] = match.match(/```(\w+)\n([\s\S]*?)```/);
        const fileName = this.inferFileName(lang, request);
        if (fileName) {
          console.log(`🎯 检测到 ${lang} 代码，创建文件: ${fileName}`);
          await this.toolExecutor.executeToolCall({
            tool: 'write_file',
            params: {
              file_path: fileName,
              content: code.trim()
            }
          });
        }
      }
    }
  }

  // 推断文件名
  inferFileName(language, request) {
    const lang = language.toLowerCase();
    if (lang === 'cpp' || lang === 'c++') return 'bubble_sort.cpp';
    if (lang === 'python' || lang === 'py') return 'bubble_sort.py';
    if (lang === 'javascript' || lang === 'js') return 'bubble_sort.js';
    if (lang === 'java') return 'BubbleSort.java';
    return null;
  }

  // 后备执行
  async fallbackExecution(request, fileContent) {
    console.log('🔄 后备执行模式...');

    if (request.includes('C++') && fileContent.includes('bubble_sort')) {
      console.log('🎯 基于请求创建 C++ 冒泡排序...');
      const cppCode = this.generateCppBubbleSort();
      await this.toolExecutor.executeToolCall({
        tool: 'write_file',
        params: {
          file_path: 'bubble_sort.cpp',
          content: cppCode
        }
      });
    }
  }

  // 生成 C++ 冒泡排序代码
  generateCppBubbleSort() {
    return `#include <iostream>
#include <vector>

void bubbleSort(std::vector<int>& arr) {
    int n = arr.size();
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                // Swap elements
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}

int main() {
    std::vector<int> data = {64, 34, 25, 12, 22, 11, 90};

    std::cout << "Original array: ";
    for (int num : data) {
        std::cout << num << " ";
    }
    std::cout << std::endl;

    bubbleSort(data);

    std::cout << "Sorted array: ";
    for (int num : data) {
        std::cout << num << " ";
    }
    std::cout << std::endl;

    return 0;
}`;
  }

  async callGeminiCLI(request) {
    return new Promise((resolve, reject) => {
      console.log('📞 调用 Gemini CLI...');

      // 使用更明确的指令
      const enhancedRequest = `${request}。请使用write_file工具创建实际的文件，不要只是展示代码。`;

      const geminiProcess = spawn('node', ['bundle/gemini.js', '--yolo', '-p', enhancedRequest], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      geminiProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      geminiProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      geminiProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Gemini CLI 调用成功');
          resolve(output);
        } else {
          console.error(`❌ Gemini CLI 调用失败 (退出码: ${code})`);
          console.error('错误输出:', errorOutput);
          resolve(output || null); // 即使失败也返回输出
        }
      });

      geminiProcess.on('error', (error) => {
        console.error('❌ 启动 Gemini CLI 失败:', error.message);
        resolve(null);
      });

      // 设置超时
      setTimeout(() => {
        geminiProcess.kill();
        console.log('⏰ Gemini CLI 调用超时');
        resolve(output || null);
      }, 30000); // 30秒超时
    });
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔧 混合工具执行器

这个工具结合了 AI 智能分析和自动工具执行：
1. 🤖 先调用 Gemini CLI 获取 AI 分析
2. 🔍 解析 AI 响应中的工具调用建议  
3. ⚡ 自动执行相应的工具操作

用法: node hybrid-tool-executor.js "你的复杂请求"

示例:
  node hybrid-tool-executor.js "请读取bubble_sort.py，按照相同逻辑实现C++版本"
  node hybrid-tool-executor.js "分析package.json并创建一个简化版本"
  node hybrid-tool-executor.js "读取所有Python文件并生成文档"

优势:
  ✅ AI 智能理解复杂请求
  ✅ 自动执行工具操作
  ✅ 无需手动确认
  ✅ 完整的执行日志
`);
    return;
  }

  const request = args.join(' ');
  const executor = new HybridExecutor();
  
  try {
    await executor.execute(request);
  } catch (error) {
    console.error('💥 执行器错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main().catch(console.error);
