# DeepSeek API 配置示例
# 复制此文件为 .env 并填入你的 API 密钥

# DeepSeek API Key
# 从 https://platform.deepseek.com/ 获取
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# 可选：指定默认模型
# 支持的模型: deepseek-chat, deepseek-coder
GEMINI_MODEL=deepseek-chat

# 可选：其他提供商的 API Key（如果需要切换）
# OPENAI_API_KEY=sk-your-openai-key
# CLAUDE_API_KEY=sk-ant-your-claude-key
# GEMINI_API_KEY=your-gemini-key

# 使用说明:
# 1. 将此文件重命名为 .env
# 2. 填入你的 DeepSeek API Key
# 3. 运行: gemini "你的问题"
# 4. 或指定模型: gemini -m deepseek-coder "写一个排序算法"
