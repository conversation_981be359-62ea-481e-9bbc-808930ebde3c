<PERSON><PERSON><PERSON>,Author,Subject,Total Lines Changed,Category
bf873a1d85d4400fd22c175db0f3b80a47bc27fb,<PERSON>,"feat: add prepublishOnly checks (#2052)",57,Medium
d9892ada7f313c10f3206d286091803126f3ea72,<PERSON>,"fix: add repository field to package.jsons (#2032)",64,Medium
560905154c9c44b3439bf6dce20c2d741bdd73af,<PERSON>,"Updating the first user message to mention the product name (#2037)",4,Small
267173c7e81425febc27e46fdcb5b5ecd5858b8d,anj-s,"Revert ""feat: Add model selection logic (#1678)"" (#1983)",99,Medium
c55b15f705d083e3dadcfb71494dcb0d6043e6c6,<PERSON><PERSON><PERSON>,"Improve LoadCodeAssist error handling (#1645)",116,Large
24ccc9c4578f40317ee903f731831f42eed699d4,<PERSON><PERSON> <PERSON>,"feat: Add model selection logic (#1678)",99,Medium
121bba346411cce23e350b833dc5857ea2239f2f,Vladislav Semyanov,"docs: fix broken configuration link in themes.md (#1780)",2,Small
02bd8dfeffa152cdf04c5a07b3f270d4933c241e,김진엽 (Nathan),"docs: fix multiple typos in documentation files (#1781)",8,Small
ee5bf842eb5a4c6f19870a3c42eea35a8e3aacef,Masato Sugiyama,"fix: remove unnecessary @gemini-code/core mock from slashCommandProcessor test (#1739)",9,Small
891116a6c2ab0a35ccabba9cbc70b7d3aea3587e,Keith Ballinger,"Flaky test (#1405)",6,Small
dbe217828d71794e85e7b03592639be0b81463cb,Noritaka Kobayashi,"chore: fix typos in comment-out (#1540)",16,Small
b8ae12a109b2d85b925502f5afea02c624e418d6,Andrew Drozdov,"Update geminiChat.ts (#1681)",2,Small
759ad4cc966df0b472505efce653c3badd2a5537,Billy Biggs,"When resuming a checkpoint always add items to history even if not shown (#1653)",6,Small
4d4b95a41d0626f9dd2c22ac80c66b18ac309293,Jennifer Davis,"fix: add engines check to ensure older Node.js versions don't fail at… (#1752)",3,Small
046c7f84bc3f7acce7467d173ff1b812574c50eb,mpAtGoogle,"Update LICENSE (#1664)",2,Small
01ff27709d7b62491bc2438fb8939da034c1c003,Brandon Keiji,"chore: bump to 0.1.5 (#1731)",16,Small
1078a546feb047122dfb6354b47e4db71acea8ac,anj-s,"Fix doc link in the auth dialog  (#1688)",12,Small
a8763abfb7eeb91bc48445a5e0a24ab0532a714d,Brandon Keiji,"feat: add release trigger configuration (#1697)",217,Large
bb797ded7d15003dfec7a68ff82764d7a2c44458,Bryan Morgan,"429 fix (#1668)",120,Large
b6b9923dc3b80a73fdee3a3ccd6070c8cfb551cd,Jerop Kipruto,"Streamline issue submission with YAML forms (#1608)",145,Large
79c647d486a5ef3cf9eb68f23000525e8d2c4a91,Tommaso Sciortino,"Merge ""Login with Google Workspace"" auth option into ""Login with Google"" (#1574)",114,Large
00b24c917e6c6f5e059fec6cb0a5b55789fa1e1e,anj-s,"Update usage stats doc (#1636)",1,Small
****************************************,Logan Kilpatrick,"Update README.md (#1632)",2,Small
5aa6b9a84b4edcbf22a160d9223e11358d588886,anj-s,"Update docs and add faq section (#1625)",41,Medium
8c5a0b6f88984208f28383cc76b6569bd2e274b0,Jerop Kipruto,"prompt users to search for existing issues before creating a new one (#1595)",2,Small
b0cf9bcecea84c15ad951edc94f09f73f8c3a192,zhiyue,"fix(telemetry): handle all promise rejections in ClearcutLogger (#1557)",21,Medium
31f32421a3b37cbfd296b02c39b112170ca5e761,Seth Troisi,"Minor style changes to prompt (#1578)",148,Large
eacbb3551ce12012a0ef9b9338b8b747b2e7161c,Bryan Morgan,"changed 429 failover from 3 consecutive to 2 for OAuth users (#1579)",16,Small
4b5ca6bc777180b2ea11f391e6f612da5072cc3e,anj-s,"Add tos and privacy links docs for clarity (#1571)",125,Large
452b82162b7b81c6c1d9ba6c6c4fe4d4431c626b,Mark McDonald,"Adding some troubleshooting text for login issues (#1451)",18,Small
aa0e3755084ad06692354b92e0b77d6e84963dbb,DongJin Jeong,"Fix: update npx command to correct GitHub repository URL (#1488)",2,Small
6a0b8a733bfe49d9d19ef5b6b9a7e6866d93a5d6,Jenna Inouye,"Add information request to bug reports: login method. (#1501)",5,Small
63a7fbc5fdda19367cac726c77773f8cb42c2f5f,Arjun Lall,"Fixed Checkpointing docs for enabling checkpointing using settings.json (#1534)",6,Small
3a369ddec3b226dea9d1a9dcc3bae048310edffd,N. Taylor Mullen,"feat: Refine model fallback messaging to reflect reality. (#1527)",5,Small
0915bf7d677504c28b079693a0fe1c853adc456e,starsandskies,"Remove GEMINI_CODE_ASSIST env variable from configuration.md doc (#1514)",5,Small
9897a2b80a6f371363faf1345f406ea581b841db,Marat Boshernitsan,"Clarify why authentication failures might be happening and direct users to documentation (#1478)",15,Small
819507c5e8f9fe54b1ac15dfc953e252982cd1ae,Jason Davenport,"Update Readme to call out gemini command after npm install (#1423)",11,Small
45b2b382cca54f67fbaec0123dee3e0f553d6163,matt korwel,"Prod: Integration Tests Main Only (#1461)",4,Small
af4dfd9327950d99cc2740b3cdd91e3186258a7a,Thomas Kosiewski,"Update authentication.md (#1429)",4,Small
21cfe9f6801f286dda6d51d2886e27bd67bd5fa4,matt korwel,"issue template update (#1441)",3,Small
6991ba1387436f2a0da995a8c3d3713f186a6544,matt korwel,"Version 0 1 1 (#1426)",35,Medium
452dca4301ad34bff8621eb68f5fcbc0796b348a,Asad Memon,"Fix typo in intro on MCP line (#1421)",2,Small
852210e1088e7de507f1acb8229c80bc0e088168,N. Taylor Mullen,"Shipping it! (#1418)",1,Small
f6c36f75e37a9fb6e53480981c2ca1b9267763a0,Brandon Keiji,"fix: prepublish changes to package names (#1420)",183,Large
a3c46c0d316eab5c17ecd9a00cab961edd24c2a4,Jenna Inouye,"Docs: Add link links to tools/index.md. (#1419)",47,Medium
f78479d7ceca5c4e5a92049c9438c3c4b1d41ae4,Mark McDonald,"Add intro text back to README (#1417)",34,Medium
cfc13fbd58f759ca09157ca24bb28b9aa7bd65e5,Mark McDonald,"Add 'npm i' instruction to README (#1416)",6,Small
9a093e4b51f3079ed3856da285f37bb21aeb483f,matt korwel,"CI: Linting Fix (#1413)",4,Small
4cc2b27f1d0169bc794f48f160b3825a64f8c042,Preston Holmes,"Docs: update docs to clarify the differences between Google account login o… (#1409)",7,Small
39bfa108b506afd3495a08e64d77f6aa1fb7d126,Scott Densmore,"refactor: remove deplicate dependency in slashCommandProcessor (#1410)",1,Small
