#!/usr/bin/env node

/**
 * 测试 DeepSeek API 的工具调用功能
 */

import { readFileSync } from 'fs';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;

if (!DEEPSEEK_API_KEY) {
  console.error('❌ DEEPSEEK_API_KEY 环境变量未设置');
  process.exit(1);
}

async function testDeepSeekToolCalling() {
  console.log('🧪 测试 DeepSeek API 工具调用功能...');
  
  const tools = [
    {
      type: 'function',
      function: {
        name: 'write_file',
        description: 'Write content to a file',
        parameters: {
          type: 'object',
          properties: {
            file_path: {
              type: 'string',
              description: 'The path of the file to write'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['file_path', 'content']
        }
      }
    }
  ];

  const messages = [
    {
      role: 'user',
      content: '请使用write_file工具创建一个名为test.txt的文件，内容为"Hello World"'
    }
  ];

  try {
    console.log('📞 调用 DeepSeek API...');
    console.log('🔧 工具定义:', JSON.stringify(tools, null, 2));
    console.log('💬 消息:', JSON.stringify(messages, null, 2));
    
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages,
        tools,
        temperature: 0.7,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API 错误: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ API 响应成功!');
    console.log('📋 完整响应:', JSON.stringify(data, null, 2));
    
    const choice = data.choices[0];
    console.log('\n🔍 分析响应:');
    console.log('📝 消息内容:', choice.message.content);
    console.log('🔧 工具调用:', choice.message.tool_calls);
    
    if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
      console.log('\n🎉 DeepSeek 成功生成了工具调用!');
      for (const toolCall of choice.message.tool_calls) {
        console.log(`🔧 工具: ${toolCall.function.name}`);
        console.log(`📋 参数: ${toolCall.function.arguments}`);
      }
    } else {
      console.log('\n❌ DeepSeek 没有生成工具调用');
      console.log('💡 可能的原因:');
      console.log('   - 模型选择了回答而不是调用工具');
      console.log('   - 工具定义不够清晰');
      console.log('   - 提示词不够明确');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

async function testWithDifferentPrompts() {
  console.log('\n🧪 测试不同的提示词...');
  
  const prompts = [
    '请使用write_file工具创建test.txt文件',
    '调用write_file函数，创建一个文件',
    'Call the write_file function to create a file named test.txt with content "Hello World"',
    '你必须使用write_file工具来创建文件，不要只是告诉我怎么做',
  ];
  
  for (let i = 0; i < prompts.length; i++) {
    console.log(`\n--- 测试 ${i + 1}: "${prompts[i]}" ---`);
    
    try {
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: prompts[i] }],
          tools: [
            {
              type: 'function',
              function: {
                name: 'write_file',
                description: 'Write content to a file',
                parameters: {
                  type: 'object',
                  properties: {
                    file_path: { type: 'string', description: 'File path' },
                    content: { type: 'string', description: 'File content' }
                  },
                  required: ['file_path', 'content']
                }
              }
            }
          ],
          temperature: 0.1, // 降低温度，让模型更倾向于调用工具
        }),
      });

      const data = await response.json();
      const choice = data.choices[0];
      
      if (choice.message.tool_calls && choice.message.tool_calls.length > 0) {
        console.log('✅ 成功生成工具调用!');
        console.log('🔧 工具调用:', choice.message.tool_calls[0].function.name);
      } else {
        console.log('❌ 没有生成工具调用');
        console.log('📝 响应内容:', choice.message.content?.substring(0, 100) + '...');
      }
      
    } catch (error) {
      console.log('❌ 请求失败:', error.message);
    }
  }
}

// 运行测试
async function main() {
  await testDeepSeekToolCalling();
  await testWithDifferentPrompts();
}

main().catch(console.error);
