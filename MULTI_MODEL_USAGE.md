# 多模型支持使用指南

这个修改版本的 Gemini CLI 现在支持多个 LLM 提供商，包括 OpenAI 和 Claude。

## 🚀 支持的模型提供商

### 1. Google Gemini (原有功能)
- **鉴权类型**: `gemini-api-key`
- **环境变量**: `GEMINI_API_KEY`
- **支持的模型**: `gemini-2.5-pro`, `gemini-2.5-flash`, `gemini-1.5-pro` 等

### 2. OpenAI (新增)
- **鉴权类型**: `openai-api-key`
- **环境变量**: `OPENAI_API_KEY`
- **支持的模型**: `gpt-4o`, `gpt-4o-mini`, `gpt-4-turbo`, `gpt-3.5-turbo` 等

### 3. <PERSON> (新增)
- **鉴权类型**: `claude-api-key`
- **环境变量**: `CLAUDE_API_KEY`
- **支持的模型**: `claude-3-5-sonnet-20241022`, `claude-3-5-haiku-20241022`, `claude-3-opus-20240229` 等

## 📝 配置方法

### 方法一：环境变量（推荐）

```bash
# 使用 OpenAI
export OPENAI_API_KEY="sk-your-openai-api-key"

# 使用 Claude
export CLAUDE_API_KEY="sk-ant-your-claude-api-key"

# 使用 Gemini（原有方式）
export GEMINI_API_KEY="your-gemini-api-key"
```

### 方法二：.env 文件

在项目根目录或 `.gemini/` 目录下创建 `.env` 文件：

```env
# 选择一个或多个
OPENAI_API_KEY=sk-your-openai-api-key
CLAUDE_API_KEY=sk-ant-your-claude-api-key
GEMINI_API_KEY=your-gemini-api-key
```

### 方法三：settings.json 配置

在 `.gemini/settings.json` 中指定鉴权类型：

```json
{
  "selectedAuthType": "openai-api-key"
}
```

## 🎯 使用示例

### 使用 OpenAI GPT-4o

```bash
# 设置环境变量
export OPENAI_API_KEY="sk-your-openai-api-key"

# 使用默认模型 (gpt-4o)
gemini "解释一下什么是机器学习"

# 指定特定模型
gemini -m gpt-4o-mini "写一个 Python 函数来计算斐波那契数列"
```

### 使用 Claude

```bash
# 设置环境变量
export CLAUDE_API_KEY="sk-ant-your-claude-api-key"

# 使用默认模型 (claude-3-5-sonnet-20241022)
gemini "帮我写一个 React 组件"

# 指定特定模型
gemini -m claude-3-5-haiku-20241022 "总结这段代码的功能"
```

### 使用 Gemini（原有方式）

```bash
# 设置环境变量
export GEMINI_API_KEY="your-gemini-api-key"

# 使用默认模型
gemini "分析这个项目的架构"
```

## 🔄 自动鉴权选择

如果没有在 settings.json 中指定 `selectedAuthType`，系统会按以下优先级自动选择：

1. `GEMINI_API_KEY` → 使用 Gemini
2. `OPENAI_API_KEY` → 使用 OpenAI
3. `CLAUDE_API_KEY` → 使用 Claude

## ⚙️ 模型默认值

- **OpenAI**: 默认使用 `gpt-4o`
- **Claude**: 默认使用 `claude-3-5-sonnet-20241022`
- **Gemini**: 默认使用 `gemini-2.5-pro`

## 🚨 注意事项

### OpenAI
- 需要有效的 OpenAI API 密钥
- 支持流式响应
- Token 计数为估算值（实际计费以 OpenAI 为准）

### Claude
- 需要有效的 Anthropic API 密钥
- 支持流式响应
- **不支持嵌入功能**（embeddings）
- Token 计数为估算值

### Gemini
- 保持原有的所有功能
- 支持完整的工具调用和嵌入功能

## 🛠️ 故障排除

### 常见错误

1. **API Key 未设置**
   ```
   Error: OPENAI_API_KEY environment variable not found
   ```
   解决：设置对应的环境变量

2. **模型不存在**
   ```
   Error: Model 'invalid-model' not found
   ```
   解决：使用支持的模型名称

3. **API 配额超限**
   ```
   Error: Rate limit exceeded
   ```
   解决：等待或升级 API 计划

### 调试模式

使用 `-d` 参数启用调试模式：

```bash
gemini -d "你的问题"
```

## 📚 API 兼容性

这个实现将不同提供商的 API 响应转换为统一的 Gemini 格式，确保：

- 工具调用功能正常工作
- 流式响应保持一致
- 错误处理统一
- 使用统计准确

## 🔮 未来扩展

这个架构设计支持轻松添加更多 LLM 提供商，如：
- Cohere
- Hugging Face
- 本地模型（Ollama）
- 其他云服务提供商

只需要实现 `ContentGenerator` 接口即可。
