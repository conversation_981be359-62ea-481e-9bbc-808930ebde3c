#!/usr/bin/env node

/**
 * 简单工具执行器
 * 直接解析请求并执行相应的文件操作，绕过 AI 模型
 */

import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { dirname, join, resolve } from 'path';
import { execSync } from 'child_process';

/**
 * 简单的请求解析器
 */
class SimpleRequestParser {
  parse(request) {
    console.log(`🔍 解析请求: "${request}"`);
    
    // 文件创建模式
    if (request.match(/(?:创建|写入|生成).*?(?:文件|代码)/i)) {
      const filePath = this.extractFilePath(request);
      const content = this.extractContent(request);
      return {
        action: 'create_file',
        filePath,
        content
      };
    }
    
    // 文件读取模式
    if (request.match(/(?:读取|查看|显示).*?文件/i)) {
      const filePath = this.extractFilePath(request);
      return {
        action: 'read_file',
        filePath
      };
    }
    
    // 命令执行模式
    if (request.match(/(?:执行|运行).*?命令/i) || request.match(/^(ls|pwd|mkdir|rm|cat|echo)/i)) {
      const command = this.extractCommand(request);
      return {
        action: 'execute_command',
        command
      };
    }
    
    // 目录列表模式
    if (request.match(/(?:列出|查看|显示).*?(?:目录|文件夹)/i) || request.match(/^ls/i)) {
      return {
        action: 'list_directory',
        directory: '.'
      };
    }
    
    return null;
  }
  
  extractFilePath(request) {
    // 特殊处理常见文件名 - 优先级最高
    if (request.includes('package.json')) return 'package.json';
    if (request.includes('README')) return 'README.md';
    if (request.includes('tsconfig')) return 'tsconfig.json';

    // 尝试提取文件路径 - 更宽泛的匹配
    const pathMatch = request.match(/([^\s]+\.(?:py|js|ts|txt|md|json|html|css|sh))/i);
    if (pathMatch) return pathMatch[1];

    // 根据内容推断文件名
    if (request.includes('快速排序') && request.includes('python')) return 'quick_sort.py';
    if (request.includes('python') || request.includes('冒泡排序')) return 'bubble_sort.py';
    if (request.includes('javascript') || request.includes('js')) return 'script.js';
    if (request.includes('html')) return 'index.html';
    return 'output.txt';
  }
  
  extractContent(request) {
    // 根据请求类型生成内容
    if (request.includes('冒泡排序') && request.includes('python')) {
      return `def bubble_sort(arr):
    """
    冒泡排序算法实现
    :param arr: 待排序的列表
    :return: 排序后的列表
    """
    n = len(arr)
    # 遍历所有数组元素
    for i in range(n):
        # 最后 i 个元素已经是排好序的
        for j in range(0, n-i-1):
            # 如果当前元素大于下一个元素，则交换
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

if __name__ == "__main__":
    # 测试示例
    example_list = [64, 34, 25, 12, 22, 11, 90]
    print("排序前:", example_list)
    sorted_list = bubble_sort(example_list)
    print("排序后:", sorted_list)`;
    }
    
    if (request.includes('快速排序') && request.includes('python')) {
      return `def quick_sort(arr):
    """
    快速排序算法实现
    """
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)

if __name__ == "__main__":
    example_list = [64, 34, 25, 12, 22, 11, 90]
    print("排序前:", example_list)
    sorted_list = quick_sort(example_list)
    print("排序后:", sorted_list)`;
    }
    
    return 'Hello World!';
  }
  
  extractCommand(request) {
    // 提取命令
    const commandMatch = request.match(/(?:执行|运行).*?命令\s*['"](.*?)['"]|(?:执行|运行)\s*([^\s]+)/i);
    if (commandMatch) return commandMatch[1] || commandMatch[2];
    
    if (request.includes('ls') || request.includes('列出')) return 'ls -la';
    if (request.includes('pwd')) return 'pwd';
    return request.trim();
  }
}

/**
 * 简单工具执行器
 */
class SimpleToolExecutor {
  constructor() {
    this.parser = new SimpleRequestParser();
  }
  
  async execute(request) {
    const parsed = this.parser.parse(request);
    
    if (!parsed) {
      console.log('❌ 无法解析请求，请尝试更明确的描述');
      console.log('💡 支持的操作:');
      console.log('   - 创建文件: "创建一个冒泡排序的python文件"');
      console.log('   - 读取文件: "读取 package.json 文件"');
      console.log('   - 执行命令: "执行命令 ls -la"');
      console.log('   - 列出目录: "列出当前目录"');
      return;
    }
    
    console.log(`✅ 解析成功: ${parsed.action}`);
    
    try {
      switch (parsed.action) {
        case 'create_file':
          await this.createFile(parsed.filePath, parsed.content);
          break;
        case 'read_file':
          await this.readFile(parsed.filePath);
          break;
        case 'execute_command':
          await this.executeCommand(parsed.command);
          break;
        case 'list_directory':
          await this.listDirectory(parsed.directory);
          break;
        default:
          console.log('❌ 未知操作');
      }
    } catch (error) {
      console.error('❌ 执行失败:', error.message);
    }
  }
  
  async createFile(filePath, content) {
    console.log(`📝 创建文件: ${filePath}`);
    
    // 确保目录存在
    const dir = dirname(filePath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
      console.log(`📁 创建目录: ${dir}`);
    }
    
    // 写入文件
    writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 文件创建成功: ${resolve(filePath)}`);
    console.log(`📊 文件大小: ${content.length} 字符`);
    console.log(`📋 内容预览:`);
    console.log(content.split('\n').slice(0, 5).join('\n'));
    if (content.split('\n').length > 5) {
      console.log('...(更多内容)');
    }
  }
  
  async readFile(filePath) {
    console.log(`📖 读取文件: ${filePath}`);
    
    if (!existsSync(filePath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      return;
    }
    
    const content = readFileSync(filePath, 'utf8');
    console.log(`✅ 文件读取成功`);
    console.log(`📊 文件大小: ${content.length} 字符`);
    console.log(`📋 文件内容:`);
    console.log(content);
  }
  
  async executeCommand(command) {
    console.log(`💻 执行命令: ${command}`);
    
    try {
      const output = execSync(command, { 
        encoding: 'utf8',
        cwd: process.cwd(),
        maxBuffer: 1024 * 1024 // 1MB
      });
      console.log(`✅ 命令执行成功`);
      console.log(`📋 输出:`);
      console.log(output);
    } catch (error) {
      console.error(`❌ 命令执行失败: ${error.message}`);
      if (error.stdout) {
        console.log(`📋 标准输出:`);
        console.log(error.stdout);
      }
      if (error.stderr) {
        console.log(`📋 错误输出:`);
        console.log(error.stderr);
      }
    }
  }
  
  async listDirectory(directory) {
    console.log(`📁 列出目录: ${directory}`);
    await this.executeCommand(`ls -la ${directory}`);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔧 简单工具执行器

用法: node simple-tool-executor.js "你的请求"

示例:
  node simple-tool-executor.js "创建一个冒泡排序的python文件"
  node simple-tool-executor.js "创建一个快速排序的python文件"
  node simple-tool-executor.js "读取 package.json 文件"
  node simple-tool-executor.js "列出当前目录"
  node simple-tool-executor.js "执行命令 ls -la"

支持的操作:
  📝 文件创建 (支持 Python 排序算法等)
  📖 文件读取
  💻 命令执行
  📁 目录列表
`);
    return;
  }

  const request = args.join(' ');
  const executor = new SimpleToolExecutor();
  
  console.log('🚀 启动简单工具执行器...');
  await executor.execute(request);
}

// 运行主函数
main().catch(console.error);
