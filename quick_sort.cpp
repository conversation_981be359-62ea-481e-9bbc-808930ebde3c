#include <iostream>
#include <vector>
#include <algorithm>

// Function to partition the array and return the pivot index
int partition(std::vector<int>& arr, int low, int high) {
    int pivot = arr[high]; // Choose the last element as the pivot
    int i = (low - 1);   // Index of smaller element

    for (int j = low; j < high; j++) {
        // If the current element is smaller than the pivot
        if (arr[j] < pivot) {
            i++; // Increment index of smaller element
            std::swap(arr[i], arr[j]);
        }
    }
    std::swap(arr[i + 1], arr[high]);
    return (i + 1);
}

// The main function that implements QuickSort
void quickSort(std::vector<int>& arr, int low, int high) {
    if (low < high) {
        // pi is partitioning index, arr[pi] is now at the right place
        int pi = partition(arr, low, high);

        // Separately sort elements before partition and after partition
        quickSort(arr, low, pi - 1);
        quickSort(arr, pi + 1, high);
    }
}

// Utility function to print a vector
void printArray(const std::vector<int>& arr) {
    for (int val : arr) {
        std::cout << val << " ";
    }
    std::cout << std::endl;
}

// Main function to test the quick sort algorithm
int main() {
    std::vector<int> arr = {10, 7, 8, 9, 1, 5};
    std::cout << "Original array: ";
    printArray(arr);

    quickSort(arr, 0, arr.size() - 1);

    std::cout << "Sorted array:   ";
    printArray(arr);

    // Test with another array
    std::vector<int> arr2 = {3, 6, 8, 10, 1, 2, 1};
     std::cout << "\nOriginal array: ";
    printArray(arr2);

    quickSort(arr2, 0, arr2.size() - 1);

    std::cout << "Sorted array:   ";
    printArray(arr2);

    return 0;
}
