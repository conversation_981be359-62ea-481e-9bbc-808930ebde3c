#!/bin/bash

# Gemini CLI 调试脚本

echo "🔍 Gemini CLI 调试工具"
echo "======================="
echo ""

# 检查环境变量
echo "📋 检查环境变量："
echo "DEEPSEEK_API_KEY: ${DEEPSEEK_API_KEY:+已设置} ${DEEPSEEK_API_KEY:-未设置}"
echo "OPENAI_API_KEY: ${OPENAI_API_KEY:+已设置} ${OPENAI_API_KEY:-未设置}"
echo "CLAUDE_API_KEY: ${CLAUDE_API_KEY:+已设置} ${CLAUDE_API_KEY:-未设置}"
echo "GEMINI_API_KEY: ${GEMINI_API_KEY:+已设置} ${GEMINI_API_KEY:-未设置}"
echo ""

# 检查 .env 文件
echo "📁 检查 .env 文件："
if [ -f ".env" ]; then
    echo "✅ .env 文件存在"
    echo "内容预览："
    grep -E "^[A-Z_]+=.*" .env | sed 's/=.*/=***/' || echo "   (空文件或无有效配置)"
else
    echo "❌ .env 文件不存在"
fi
echo ""

# 检查网络连接
echo "🌐 检查网络连接："
echo "测试 DeepSeek API..."
if curl -s --connect-timeout 5 https://api.deepseek.com > /dev/null; then
    echo "✅ DeepSeek API 可达"
else
    echo "❌ DeepSeek API 不可达"
fi

echo "测试 OpenAI API..."
if curl -s --connect-timeout 5 https://api.openai.com > /dev/null; then
    echo "✅ OpenAI API 可达"
else
    echo "❌ OpenAI API 不可达"
fi

echo "测试 Claude API..."
if curl -s --connect-timeout 5 https://api.anthropic.com > /dev/null; then
    echo "✅ Claude API 可达"
else
    echo "❌ Claude API 不可达"
fi
echo ""

# 检查 Node.js 版本
echo "⚙️  检查运行环境："
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo ""

# 检查项目文件
echo "📦 检查项目文件："
if [ -f "bundle/gemini.js" ]; then
    echo "✅ bundle/gemini.js 存在"
    echo "文件大小: $(ls -lh bundle/gemini.js | awk '{print $5}')"
else
    echo "❌ bundle/gemini.js 不存在，需要重新构建"
fi
echo ""

# 提供调试建议
echo "🔧 调试建议："
echo "1. 确保 API Key 正确设置在 .env 文件中"
echo "2. 检查网络连接和代理设置"
echo "3. 尝试使用 -d 参数启用调试模式："
echo "   node bundle/gemini.js -d \"测试消息\""
echo "4. 尝试指定具体模型："
echo "   node bundle/gemini.js -m deepseek-chat \"测试消息\""
echo ""

# 运行简单测试
echo "🧪 运行简单测试："
if [ -n "$DEEPSEEK_API_KEY" ]; then
    echo "测试 DeepSeek API 连接..."
    curl -s -X POST https://api.deepseek.com/v1/chat/completions \
        -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
        -H "Content-Type: application/json" \
        -d '{
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10
        }' | jq '.error // "API 连接正常"' 2>/dev/null || echo "API 测试失败或 jq 未安装"
elif [ -n "$OPENAI_API_KEY" ]; then
    echo "测试 OpenAI API 连接..."
    curl -s -X POST https://api.openai.com/v1/chat/completions \
        -H "Authorization: Bearer $OPENAI_API_KEY" \
        -H "Content-Type: application/json" \
        -d '{
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "Hello"}],
            "max_tokens": 10
        }' | jq '.error // "API 连接正常"' 2>/dev/null || echo "API 测试失败或 jq 未安装"
else
    echo "未设置 API Key，跳过 API 测试"
fi
echo ""

echo "🎯 如果问题仍然存在，请："
echo "1. 检查完整的错误日志"
echo "2. 确认 API Key 有效性"
echo "3. 尝试重新构建项目: npm run build"
echo "4. 查看详细错误信息: node --trace-warnings bundle/gemini.js"
