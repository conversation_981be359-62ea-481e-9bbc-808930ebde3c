#!/usr/bin/env node

/**
 * 直接工具执行器
 * 绕过 AI 模型，直接解析请求并调用相应的工具
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 动态导入构建后的 ES 模块
const { Config, createToolRegistry } = await import('./packages/core/dist/src/config/config.js');

/**
 * 请求解析器 - 将自然语言请求转换为工具调用
 */
class RequestParser {
  constructor() {
    this.patterns = [
      // 文件创建/写入
      {
        pattern: /(?:创建|写入|生成).*?文件.*?(?:内容|代码).*?['"](.*?)['"]|(?:创建|写入|生成).*?文件\s*([^\s]+).*?内容.*?['"](.*?)['"]|(?:创建|写入|生成).*?([^\s]+\.(?:py|js|ts|txt|md|json|html|css)).*?(?:内容|代码)/i,
        tool: 'write_file',
        extractor: (match, request) => {
          const content = match[1] || match[3] || this.extractCodeFromRequest(request);
          const filePath = match[2] || match[4] || this.extractFilePathFromRequest(request);
          return { file_path: filePath, content };
        }
      },
      // 文件读取
      {
        pattern: /(?:读取|查看|显示).*?文件\s*([^\s]+)|(?:读取|查看|显示)\s*([^\s]+\.(?:py|js|ts|txt|md|json|html|css))/i,
        tool: 'read_file',
        extractor: (match) => ({ file_path: match[1] || match[2] })
      },
      // 文件编辑/替换
      {
        pattern: /(?:修改|编辑|替换).*?文件\s*([^\s]+).*?将\s*['"](.*?)['"].*?替换为\s*['"](.*?)['"]|(?:修改|编辑|替换)\s*([^\s]+\.(?:py|js|ts|txt|md|json|html|css))/i,
        tool: 'replace',
        extractor: (match) => ({
          file_path: match[1] || match[4],
          old_string: match[2] || '',
          new_string: match[3] || ''
        })
      },
      // Shell 命令执行
      {
        pattern: /(?:执行|运行).*?命令\s*['"](.*?)['"]|(?:执行|运行)\s*([^\s]+)|ls|pwd|mkdir|rm/i,
        tool: 'shell',
        extractor: (match, request) => ({
          command: match[1] || match[2] || this.extractCommandFromRequest(request)
        })
      },
      // 目录列表
      {
        pattern: /(?:列出|查看|显示).*?(?:目录|文件夹|文件).*?内容|ls\s*(.*)|dir\s*(.*)/i,
        tool: 'ls',
        extractor: (match) => ({ directory: match[1] || match[2] || '.' })
      }
    ];
  }

  extractFilePathFromRequest(request) {
    // 尝试从请求中提取文件路径
    const pathMatch = request.match(/([^\s]+\.(?:py|js|ts|txt|md|json|html|css|sh))/i);
    if (pathMatch) return pathMatch[1];
    
    // 如果没有找到，根据内容类型推断
    if (request.includes('python') || request.includes('冒泡排序')) return 'bubble_sort.py';
    if (request.includes('javascript') || request.includes('js')) return 'script.js';
    if (request.includes('html')) return 'index.html';
    return 'output.txt';
  }

  extractCodeFromRequest(request) {
    // 尝试从请求中提取代码内容
    if (request.includes('冒泡排序') && request.includes('python')) {
      return `def bubble_sort(arr):
    """
    冒泡排序算法实现
    :param arr: 待排序的列表
    :return: 排序后的列表
    """
    n = len(arr)
    for i in range(n):
        for j in range(0, n-i-1):
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

if __name__ == "__main__":
    example_list = [64, 34, 25, 12, 22, 11, 90]
    print("排序前:", example_list)
    sorted_list = bubble_sort(example_list)
    print("排序后:", sorted_list)`;
    }
    return 'Hello World';
  }

  extractCommandFromRequest(request) {
    // 从请求中提取命令
    if (request.includes('ls') || request.includes('列出')) return 'ls -la';
    if (request.includes('pwd')) return 'pwd';
    return 'echo "Hello World"';
  }

  parse(request) {
    console.log(`🔍 解析请求: "${request}"`);
    
    for (const { pattern, tool, extractor } of this.patterns) {
      const match = request.match(pattern);
      if (match) {
        const params = extractor(match, request);
        console.log(`✅ 匹配到工具: ${tool}, 参数:`, params);
        return { tool, params };
      }
    }
    
    console.log('❌ 未找到匹配的工具模式');
    return null;
  }
}

/**
 * 直接工具执行器
 */
class DirectToolExecutor {
  constructor() {
    this.parser = new RequestParser();
    this.config = null;
    this.toolRegistry = null;
  }

  async initialize() {
    console.log('🚀 初始化工具执行器...');
    
    // 创建配置
    this.config = new Config({
      targetDir: process.cwd(),
      approvalMode: 'YOLO', // 直接执行，无需确认
      model: 'gemini-2.5-pro'
    });

    // 创建工具注册表
    this.toolRegistry = await createToolRegistry(this.config);
    
    console.log('✅ 初始化完成');
  }

  async execute(request) {
    if (!this.config || !this.toolRegistry) {
      await this.initialize();
    }

    // 解析请求
    const parsed = this.parser.parse(request);
    if (!parsed) {
      console.log('❌ 无法解析请求，请尝试更明确的描述');
      return;
    }

    const { tool: toolName, params } = parsed;

    // 获取工具实例
    const toolRegistry = await this.toolRegistry;
    const tool = toolRegistry.getTool(toolName);
    
    if (!tool) {
      console.log(`❌ 工具 "${toolName}" 未找到`);
      return;
    }

    console.log(`🔧 执行工具: ${toolName}`);
    console.log(`📋 参数:`, JSON.stringify(params, null, 2));

    try {
      // 创建 AbortController
      const abortController = new AbortController();
      
      // 执行工具
      const result = await tool.execute(params, abortController.signal);
      
      console.log('✅ 执行成功!');
      console.log('📤 结果:', result.llmContent || result.returnDisplay);
      
      if (result.returnDisplay && typeof result.returnDisplay === 'object') {
        console.log('📊 详细信息:', result.returnDisplay);
      }
      
    } catch (error) {
      console.error('❌ 执行失败:', error.message);
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔧 直接工具执行器

用法: node direct-tool-executor.js "你的请求"

示例:
  node direct-tool-executor.js "创建一个冒泡排序的python文件"
  node direct-tool-executor.js "读取 package.json 文件"
  node direct-tool-executor.js "列出当前目录的文件"
  node direct-tool-executor.js "执行命令 ls -la"

支持的操作:
  📝 文件创建/写入
  📖 文件读取
  ✏️  文件编辑/替换
  💻 Shell 命令执行
  📁 目录列表
`);
    return;
  }

  const request = args.join(' ');
  const executor = new DirectToolExecutor();
  
  try {
    await executor.execute(request);
  } catch (error) {
    console.error('💥 执行器错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main().catch(console.error);
