#!/usr/bin/env node

/**
 * 简单的 DeepSeek 集成测试脚本
 * 用于验证 DeepSeek API 集成是否正常工作
 */

import { DeepSeekContentGenerator } from './packages/core/src/core/deepseekContentGenerator.js';

async function testDeepSeekIntegration() {
  console.log('🧪 测试 DeepSeek 集成...\n');

  // 检查环境变量
  const apiKey = process.env.DEEPSEEK_API_KEY;
  if (!apiKey) {
    console.error('❌ 错误: 请设置 DEEPSEEK_API_KEY 环境变量');
    console.log('   export DEEPSEEK_API_KEY="sk-your-deepseek-api-key"');
    process.exit(1);
  }

  try {
    // 创建 DeepSeek 内容生成器
    const generator = new DeepSeekContentGenerator(apiKey);
    console.log('✅ DeepSeek 内容生成器创建成功');

    // 测试基本的内容生成
    console.log('\n📝 测试基本内容生成...');
    const request = {
      contents: [
        {
          role: 'user',
          parts: [{ text: '请简单介绍一下 DeepSeek 模型的特点' }]
        }
      ],
      model: 'deepseek-chat'
    };

    const response = await generator.generateContent(request);
    
    if (response.candidates && response.candidates.length > 0) {
      const text = response.candidates[0].content.parts[0].text;
      console.log('✅ 内容生成成功');
      console.log('📄 响应内容:', text.substring(0, 200) + '...');
      
      if (response.usageMetadata) {
        console.log('📊 Token 使用情况:');
        console.log(`   - 输入 tokens: ${response.usageMetadata.promptTokenCount}`);
        console.log(`   - 输出 tokens: ${response.usageMetadata.candidatesTokenCount}`);
        console.log(`   - 总计 tokens: ${response.usageMetadata.totalTokenCount}`);
      }
    } else {
      console.log('❌ 未收到有效响应');
    }

    // 测试 Token 计数
    console.log('\n🔢 测试 Token 计数...');
    const tokenRequest = {
      contents: [
        {
          role: 'user',
          parts: [{ text: '这是一个测试文本，用于计算 token 数量。' }]
        }
      ]
    };

    const tokenResponse = await generator.countTokens(tokenRequest);
    console.log(`✅ Token 计数: ${tokenResponse.totalTokens} tokens`);

    // 测试流式响应
    console.log('\n🌊 测试流式响应...');
    const streamRequest = {
      contents: [
        {
          role: 'user',
          parts: [{ text: '请写一个简单的 Python Hello World 程序' }]
        }
      ],
      model: 'deepseek-chat'
    };

    console.log('📡 开始流式响应...');
    let streamContent = '';
    let chunkCount = 0;

    for await (const chunk of generator.generateContentStream(streamRequest)) {
      if (chunk.candidates && chunk.candidates[0]?.content?.parts?.[0]?.text) {
        const chunkText = chunk.candidates[0].content.parts[0].text;
        streamContent += chunkText;
        chunkCount++;
        process.stdout.write(chunkText);
      }
    }

    console.log(`\n✅ 流式响应完成，收到 ${chunkCount} 个数据块`);
    console.log(`📄 完整内容长度: ${streamContent.length} 字符`);

    console.log('\n🎉 所有测试通过！DeepSeek 集成工作正常。');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    
    if (error.message.includes('401')) {
      console.log('💡 提示: 请检查 DEEPSEEK_API_KEY 是否正确');
    } else if (error.message.includes('429')) {
      console.log('💡 提示: API 请求频率限制，请稍后重试');
    } else if (error.message.includes('ENOTFOUND')) {
      console.log('💡 提示: 网络连接问题，请检查网络设置');
    }
    
    process.exit(1);
  }
}

// 运行测试
testDeepSeekIntegration().catch(console.error);
