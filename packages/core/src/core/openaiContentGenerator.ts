/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
  Content,
  Part,
  GenerateContentResponseUsageMetadata,
} from '@google/genai';
import { ContentGenerator } from './contentGenerator.js';

export interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenAIContentGenerator implements ContentGenerator {
  constructor(
    private apiKey: string,
    private baseUrl: string = 'https://api.openai.com/v1',
  ) {}

  async generateContent(
    request: GenerateContentParameters,
  ): Promise<GenerateContentResponse> {
    const messages = this.convertToOpenAIMessages(request.contents);
    const model = request.model || 'gpt-4o';

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: request.config?.generationConfig?.temperature || 0.7,
        max_tokens: request.config?.generationConfig?.maxOutputTokens || 4096,
      }),
      signal: request.config?.abortSignal,
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data: OpenAIResponse = await response.json();
    return this.convertToGeminiResponse(data);
  }

  async generateContentStream(
    request: GenerateContentParameters,
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    const messages = this.convertToOpenAIMessages(request.contents);
    const model = request.model || 'gpt-4o';

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: request.config?.generationConfig?.temperature || 0.7,
        max_tokens: request.config?.generationConfig?.maxOutputTokens || 4096,
        stream: true,
      }),
      signal: request.config?.abortSignal,
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    return this.parseStreamResponse(response);
  }

  async countTokens(request: CountTokensParameters): Promise<CountTokensResponse> {
    // OpenAI doesn't have a direct token counting API, so we estimate
    const text = this.extractTextFromContents(request.contents);
    const estimatedTokens = Math.ceil(text.length / 4); // Rough estimation: 1 token ≈ 4 characters
    
    return {
      totalTokens: estimatedTokens,
    };
  }

  async embedContent(
    request: EmbedContentParameters,
  ): Promise<EmbedContentResponse> {
    const text = this.extractTextFromContents([request.content]);
    const model = request.model || 'text-embedding-3-small';

    const response = await fetch(`${this.baseUrl}/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        input: text,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return {
      embedding: {
        values: data.data[0].embedding,
      },
    };
  }

  private convertToOpenAIMessages(contents: Content[]): OpenAIMessage[] {
    const messages: OpenAIMessage[] = [];
    
    for (const content of contents) {
      const text = this.extractTextFromParts(content.parts || []);
      if (text) {
        messages.push({
          role: content.role === 'model' ? 'assistant' : (content.role as 'user' | 'system'),
          content: text,
        });
      }
    }
    
    return messages;
  }

  private extractTextFromParts(parts: Part[]): string {
    return parts
      .map(part => part.text || '')
      .filter(text => text.length > 0)
      .join('\n');
  }

  private extractTextFromContents(contents: Content[]): string {
    return contents
      .map(content => this.extractTextFromParts(content.parts || []))
      .filter(text => text.length > 0)
      .join('\n');
  }

  private convertToGeminiResponse(data: OpenAIResponse): GenerateContentResponse {
    const response = new GenerateContentResponse();
    
    if (data.choices && data.choices.length > 0) {
      response.candidates = [{
        content: {
          parts: [{ text: data.choices[0].message.content }],
          role: 'model',
        },
        finishReason: this.mapFinishReason(data.choices[0].finish_reason),
        index: 0,
      }];
    }

    if (data.usage) {
      response.usageMetadata = {
        promptTokenCount: data.usage.prompt_tokens,
        candidatesTokenCount: data.usage.completion_tokens,
        totalTokenCount: data.usage.total_tokens,
      } as GenerateContentResponseUsageMetadata;
    }

    return response;
  }

  private mapFinishReason(reason: string): string {
    switch (reason) {
      case 'stop':
        return 'STOP';
      case 'length':
        return 'MAX_TOKENS';
      case 'content_filter':
        return 'SAFETY';
      default:
        return 'OTHER';
    }
  }

  private async *parseStreamResponse(response: Response): AsyncGenerator<GenerateContentResponse> {
    const reader = response.body?.getReader();
    if (!reader) return;

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              if (parsed.choices && parsed.choices[0]?.delta?.content) {
                const response = new GenerateContentResponse();
                response.candidates = [{
                  content: {
                    parts: [{ text: parsed.choices[0].delta.content }],
                    role: 'model',
                  },
                  finishReason: 'STOP',
                  index: 0,
                }];
                yield response;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}
