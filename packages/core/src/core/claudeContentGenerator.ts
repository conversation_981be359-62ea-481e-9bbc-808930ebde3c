/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
  Content,
  Part,
  GenerateContentResponseUsageMetadata,
} from '@google/genai';
import { ContentGenerator } from './contentGenerator.js';

export interface ClaudeMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface ClaudeResponse {
  content: Array<{
    text: string;
    type: 'text';
  }>;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
  stop_reason: string;
}

export class ClaudeContentGenerator implements ContentGenerator {
  constructor(
    private apiKey: string,
    private baseUrl: string = 'https://api.anthropic.com/v1',
  ) {}

  async generateContent(
    request: GenerateContentParameters,
  ): Promise<GenerateContentResponse> {
    const { messages, system } = this.convertToClaudeMessages(request.contents);
    const model = request.model || 'claude-3-5-sonnet-20241022';

    const requestBody: any = {
      model,
      messages,
      max_tokens: request.config?.generationConfig?.maxOutputTokens || 4096,
      temperature: request.config?.generationConfig?.temperature || 0.7,
    };

    if (system) {
      requestBody.system = system;
    }

    const response = await fetch(`${this.baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify(requestBody),
      signal: request.config?.abortSignal,
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.status} ${response.statusText}`);
    }

    const data: ClaudeResponse = await response.json();
    return this.convertToGeminiResponse(data);
  }

  async generateContentStream(
    request: GenerateContentParameters,
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    const { messages, system } = this.convertToClaudeMessages(request.contents);
    const model = request.model || 'claude-3-5-sonnet-20241022';

    const requestBody: any = {
      model,
      messages,
      max_tokens: request.config?.generationConfig?.maxOutputTokens || 4096,
      temperature: request.config?.generationConfig?.temperature || 0.7,
      stream: true,
    };

    if (system) {
      requestBody.system = system;
    }

    const response = await fetch(`${this.baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify(requestBody),
      signal: request.config?.abortSignal,
    });

    if (!response.ok) {
      throw new Error(`Claude API error: ${response.status} ${response.statusText}`);
    }

    return this.parseStreamResponse(response);
  }

  async countTokens(request: CountTokensParameters): Promise<CountTokensResponse> {
    // Claude doesn't have a direct token counting API, so we estimate
    const text = this.extractTextFromContents(request.contents);
    const estimatedTokens = Math.ceil(text.length / 3.5); // Rough estimation for Claude
    
    return {
      totalTokens: estimatedTokens,
    };
  }

  async embedContent(
    _request: EmbedContentParameters,
  ): Promise<EmbedContentResponse> {
    // Claude doesn't provide embeddings API, throw error
    throw new Error('Claude API does not support embeddings. Please use a different provider for embedding functionality.');
  }

  private convertToClaudeMessages(contents: Content[]): { messages: ClaudeMessage[]; system?: string } {
    const messages: ClaudeMessage[] = [];
    let system: string | undefined;
    
    for (const content of contents) {
      const text = this.extractTextFromParts(content.parts || []);
      if (text) {
        if (content.role === 'system') {
          system = text;
        } else {
          messages.push({
            role: content.role === 'model' ? 'assistant' : 'user',
            content: text,
          });
        }
      }
    }
    
    return { messages, system };
  }

  private extractTextFromParts(parts: Part[]): string {
    return parts
      .map(part => part.text || '')
      .filter(text => text.length > 0)
      .join('\n');
  }

  private extractTextFromContents(contents: Content[]): string {
    return contents
      .map(content => this.extractTextFromParts(content.parts || []))
      .filter(text => text.length > 0)
      .join('\n');
  }

  private convertToGeminiResponse(data: ClaudeResponse): GenerateContentResponse {
    const response = new GenerateContentResponse();
    
    if (data.content && data.content.length > 0) {
      const text = data.content
        .filter(item => item.type === 'text')
        .map(item => item.text)
        .join('');

      response.candidates = [{
        content: {
          parts: [{ text }],
          role: 'model',
        },
        finishReason: this.mapFinishReason(data.stop_reason),
        index: 0,
      }];
    }

    if (data.usage) {
      response.usageMetadata = {
        promptTokenCount: data.usage.input_tokens,
        candidatesTokenCount: data.usage.output_tokens,
        totalTokenCount: data.usage.input_tokens + data.usage.output_tokens,
      } as GenerateContentResponseUsageMetadata;
    }

    return response;
  }

  private mapFinishReason(reason: string): string {
    switch (reason) {
      case 'end_turn':
        return 'STOP';
      case 'max_tokens':
        return 'MAX_TOKENS';
      case 'stop_sequence':
        return 'STOP';
      default:
        return 'OTHER';
    }
  }

  private async *parseStreamResponse(response: Response): AsyncGenerator<GenerateContentResponse> {
    const reader = response.body?.getReader();
    if (!reader) return;

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                const response = new GenerateContentResponse();
                response.candidates = [{
                  content: {
                    parts: [{ text: parsed.delta.text }],
                    role: 'model',
                  },
                  finishReason: 'STOP',
                  index: 0,
                }];
                yield response;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}
