/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import {
  CountTokensResponse,
  GenerateContentResponse,
  GenerateContentParameters,
  CountTokensParameters,
  EmbedContentResponse,
  EmbedContentParameters,
  Content,
  Part,
  GenerateContentResponseUsageMetadata,
} from '@google/genai';
import { ContentGenerator } from './contentGenerator.js';

export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DeepSeekContentGenerator implements ContentGenerator {
  constructor(
    private apiKey: string,
    private baseUrl: string = 'https://api.deepseek.com/v1',
  ) {}

  async generateContent(
    request: GenerateContentParameters,
  ): Promise<GenerateContentResponse> {
    const messages = this.convertToDeepSeekMessages(request.contents);
    const model = request.model || 'deepseek-chat';

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: request.config?.generationConfig?.temperature || 0.7,
        max_tokens: request.config?.generationConfig?.maxOutputTokens || 4096,
        stream: false,
      }),
      signal: request.config?.abortSignal,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data: DeepSeekResponse = await response.json();
    return this.convertToGeminiResponse(data);
  }

  async generateContentStream(
    request: GenerateContentParameters,
  ): Promise<AsyncGenerator<GenerateContentResponse>> {
    const messages = this.convertToDeepSeekMessages(request.contents);
    const model = request.model || 'deepseek-chat';

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: request.config?.generationConfig?.temperature || 0.7,
        max_tokens: request.config?.generationConfig?.maxOutputTokens || 4096,
        stream: true,
      }),
      signal: request.config?.abortSignal,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return this.parseStreamResponse(response);
  }

  async countTokens(request: CountTokensParameters): Promise<CountTokensResponse> {
    // DeepSeek doesn't have a direct token counting API, so we estimate
    const text = this.extractTextFromContents(request.contents);
    // DeepSeek uses similar tokenization to GPT models, roughly 1 token ≈ 4 characters for English
    const estimatedTokens = Math.ceil(text.length / 4);
    
    return {
      totalTokens: estimatedTokens,
    };
  }

  async embedContent(
    request: EmbedContentParameters,
  ): Promise<EmbedContentResponse> {
    // DeepSeek doesn't provide embeddings API in their standard offering
    throw new Error('DeepSeek API does not support embeddings. Please use a different provider for embedding functionality.');
  }

  private convertToDeepSeekMessages(contents: Content[]): DeepSeekMessage[] {
    const messages: DeepSeekMessage[] = [];
    
    for (const content of contents) {
      const text = this.extractTextFromParts(content.parts || []);
      if (text) {
        messages.push({
          role: content.role === 'model' ? 'assistant' : (content.role as 'user' | 'system'),
          content: text,
        });
      }
    }
    
    return messages;
  }

  private extractTextFromParts(parts: Part[]): string {
    return parts
      .map(part => part.text || '')
      .filter(text => text.length > 0)
      .join('\n');
  }

  private extractTextFromContents(contents: Content[]): string {
    return contents
      .map(content => this.extractTextFromParts(content.parts || []))
      .filter(text => text.length > 0)
      .join('\n');
  }

  private convertToGeminiResponse(data: DeepSeekResponse): GenerateContentResponse {
    const response = new GenerateContentResponse();
    
    if (data.choices && data.choices.length > 0) {
      response.candidates = [{
        content: {
          parts: [{ text: data.choices[0].message.content }],
          role: 'model',
        },
        finishReason: this.mapFinishReason(data.choices[0].finish_reason),
        index: 0,
      }];
    }

    if (data.usage) {
      response.usageMetadata = {
        promptTokenCount: data.usage.prompt_tokens,
        candidatesTokenCount: data.usage.completion_tokens,
        totalTokenCount: data.usage.total_tokens,
      } as GenerateContentResponseUsageMetadata;
    }

    return response;
  }

  private mapFinishReason(reason: string): string {
    switch (reason) {
      case 'stop':
        return 'STOP';
      case 'length':
        return 'MAX_TOKENS';
      case 'content_filter':
        return 'SAFETY';
      default:
        return 'OTHER';
    }
  }

  private async *parseStreamResponse(response: Response): AsyncGenerator<GenerateContentResponse> {
    const reader = response.body?.getReader();
    if (!reader) return;

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;

            try {
              const parsed = JSON.parse(data);
              if (parsed.choices && parsed.choices[0]?.delta?.content) {
                const response = new GenerateContentResponse();
                response.candidates = [{
                  content: {
                    parts: [{ text: parsed.choices[0].delta.content }],
                    role: 'model',
                  },
                  finishReason: 'STOP',
                  index: 0,
                }];
                yield response;
              }
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}
